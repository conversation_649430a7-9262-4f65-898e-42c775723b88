const ExpressError = require('@build-connect/utils/ExpressError');
const { withTransaction } = require('@build-connect/utils/transaction');
const Site = require('../model/site');
const EncumbranceCertificate = require('../model/encumbranceCertificate');
const PropertyTaxReceipt = require('../model/propertyTaxRecipt');

const {
    createAssetsHandler,
    getAssetsByEntity,
} = require('./assetscontroller');

// Add a new site
exports.addSite = async (req, res) => {
    const {
        name,
        addressLine1,
        addressLine2,
        landmark,
        location,
        pincode,
        state,
        district,
        plotArea,
        price,
        latitude,
        longitude,
    } = req.body;

    // setting userid
    const userId = req.user.id;

    await withTransaction(async (session) => {
        const newSite = new Site({
            name,
            addressLine1,
            addressLine2,
            landmark,
            location,
            pincode,
            state,
            district,
            plotArea,
            price,
            latitude,
            longitude,
            userId: userId,
        });

        const savedSite = await newSite.save({ session });

        const { siteImages, encumbranceCertificate, propertyTaxReceipt } =
            req.files || {};

        if (siteImages && siteImages.length > 0) {
            await createAssetsHandler({
                files: siteImages,
                entityId: savedSite._id,
                assetType: 'site_image',
                session,
            });
        }

        if (encumbranceCertificate && encumbranceCertificate[0]) {
            const {
                encOwnerName,
                encDocumentNo,
                surveyNo,
                village,
                subDistrict,
                District,
            } = req.body;

            const encCertificate = await new EncumbranceCertificate({
                encOwnerName,
                encDocumentNo,
                surveyNo,
                village,
                subDistrict,
                District,
                entityId: savedSite._id,
            }).save({ session });

            await createAssetsHandler({
                files: [encumbranceCertificate[0]],
                entityId: encCertificate._id,
                assetType: 'encumbrance_certificate',
                session,
            });
        } else {
            res.status(400).json({
                message: 'encumbrance certificate is required',
            });
            return;
        }

        if (propertyTaxReceipt && propertyTaxReceipt[0]) {
            const { ptrOwnerName, ptrReciptNo } = req.body;

            const ptReceipt = await new PropertyTaxReceipt({
                ptrOwnerName,
                ptrReciptNo,
                entityId: savedSite._id,
            }).save({ session });

            await createAssetsHandler({
                files: [propertyTaxReceipt[0]],
                entityId: ptReceipt._id,
                assetType: 'property_tax_receipt',
                session,
            });
        }

        res.status(201).json({
            message: 'Site added successfully',
        });
    });
};

// GET single data with its assets using id
exports.getSiteById = async (req, res) => {
    const siteId = req.params.id;

    const site = await Site.findById(siteId);
    if (!site) {
        throw new ExpressError('Site not found', 404);
    }

    const encCertificate = await EncumbranceCertificate.findOne({
        entityId: siteId,
    });
    const ptReceipt = await PropertyTaxReceipt.findOne({
        entityId: siteId,
    });

    const siteAssets = await getAssetsByEntity(site._id);
    let encAssets = [];

    if (encCertificate) {
        encAssets = await getAssetsByEntity(encCertificate._id);
    }

    let ptrAsset = [];

    if (ptReceipt) {
        ptrAsset = await getAssetsByEntity(ptReceipt._id);
    }
    res.status(200).json({
        site,
        encCertificate,
        ptReceipt,
        siteAssets,
        encAssets,
        ptrAsset,
    });
};

// get site by user
exports.getSitesByUser = async (req, res) => {
    const userId = req.user.id;

    const sites = await Site.find({ userId: userId }).lean();

    const sitesWithDetails = await Promise.all(
        sites.map(async (site) => {
            const siteAssets = await getAssetsByEntity(site._id);
            const [thumbnail] = siteAssets;
            return { ...site, thumbnail };
        })
    );

    res.status(200).json({ sites: sitesWithDetails });
};

// GET filtered sites
exports.getSites = async (req, res) => {
    const {
        search,
        sortBy = 'createdAt',
        order = 'desc',
        limit = '10',
        page = '1',
        state,
        district,
        pincode,
        latitude,
        longitude,
        distance = '5000',
    } = req.query;

    /*  2. Build base “approved” filter  */
    const filter = { status: 'approved' };

    if (search) filter.$text = { $search: search };
    if (state) filter.state = state;
    if (district) filter.district = district;
    if (pincode) filter.pincode = pincode;

    /*  3. Sort & pagination settings  */
    const sort = { [sortBy]: order === 'asc' ? 1 : -1 };
    const lim = Math.min(parseInt(limit, 10), 100);
    const pg = Math.max(parseInt(page, 10), 1);
    const skip = (pg - 1) * lim;

    /*  4. Query: geoNear or simple find  */
    let sites;
    if (latitude && longitude) {
        sites = await Site.aggregate([
            {
                $geoNear: {
                    near: {
                        type: 'Point',
                        coordinates: [+longitude, +latitude],
                    },
                    distanceField: 'distance',
                    maxDistance: +distance,
                    spherical: true,
                    query: filter,
                },
            },
            { $sort: sort },
            { $skip: skip },
            { $limit: lim },
        ]);
    } else {
        sites = await Site.find(filter).sort(sort).skip(skip).limit(lim);
    }

    const data = await Promise.all(
        sites.map(async (site) => {
            const siteAssets = await getAssetsByEntity(site._id);
            const [thumbnail] = siteAssets;
            return { ...site.toJSON(), thumbnail };
        })
    );

    res.json({ page: pg, sites: data });
};
