const ServiceRequest = require('../../model/serviceRequest');
const Project = require('../../model/project');
const ProjectRequirement = require('../../model/projectRequirement');

exports.getServiceRequests = async (req, res) => {
    const recipientId = req.user.id;

    const serviceRequests = await ServiceRequest.find({ recipientId }).lean();
    if (!serviceRequests.length) {
        return res.status(200).json({ data: [] });
    }

    const response = await Promise.all(
        serviceRequests.map(async (serviceRequest) => {
            const [project, requirement] = await Promise.all([
                Project.findOne({ _id: serviceRequest.projectId }).lean(),
                ProjectRequirement.findOne({
                    projectId: serviceRequest.projectId,
                }).lean(),
            ]);

            return {
                projectName: project?.projectName || 'Unknown',
                location: requirement?.location || null,
            };
        })
    );

    res.status(200).json({ serviceRequests: response });
};
