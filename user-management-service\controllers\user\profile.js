const ExpressError = require('@build-connect/utils/ExpressError');
const User = require('../../model/user');
const Asset = require('../../model/Asset');
const { createAssetFromFile } = require('../assets');

// Get user profile
exports.getUserProfile = async (req, res) => {
    const userId = req.user.id;

    const user = await User.findById(userId).select('-password');

    if (!user) {
        throw new ExpressError('User not found', 404);
    }

    const avatar = await Asset.findOne({
        entityId: user._id,
        assetType: 'avatar',
    });

    res.status(200).json({
        user: {
            id: user._id,
            name: user.name,
            email: user.email,
            phone: user.phone,
            avatar: avatar ? avatar.imageURL : null,
            role: user.role,
            location: user.location,
            verificationStatus: user.verificationStatus,
            isEmailVerified: user.isEmailVerified,
            isPhoneVerified: user.isPhoneVerified,
            isAvailable: user.isAvailable,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt,
            partnershipRequest: user.partnershipRequest,
        },
    });
};

// Update user profile
exports.updateUserProfile = async (req, res) => {
    const userId = req.user.id;
    const { name, phone, location } = req.body;

    const user = await User.findById(userId);

    if (!user) {
        throw new ExpressError('User not found', 404);
    }

    const updateData = {};
    if (name) updateData.name = name;
    if (phone) updateData.phone = phone;
    if (location) updateData.location = location;

    try {
        await User.updateOne({ _id: userId }, updateData, {
            runValidators: true,
        });
    } catch (error) {
        if (error.code === 11000 && error.keyPattern?.phone) {
            throw new ExpressError('Phone number already exists', 409);
        }
        throw error;
    }

    // Handle avatar upload
    if (req.file && req.file.path) {
        await createAssetFromFile(req.file, userId, 'User', 'avatar');
    }

    res.status(200).json({
        message: 'User profile updated successfully',
    });
};
