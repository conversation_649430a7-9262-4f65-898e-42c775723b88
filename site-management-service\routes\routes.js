const express = require('express');

const router = express.Router();
const { catchAsync } = require('@build-connect/utils');
const {
    handleValidationErrors,
} = require('@build-connect/utils/middleware/expressValidator');
const createAuthMiddleware = require('@build-connect/utils/middleware/authmiddleware');
const multer = require('multer');
const siteController = require('../controllers/sitecontroller');
const updateSite = require('../controllers/updatesite');
const deleteSite = require('../controllers/deletesite');
const projectRequirement = require('../controllers/projectRequirements/create');
const { updateProject } = require('../controllers/projectRequirements/update');
const {
    createServiceRequest,
} = require('../controllers/serviceRequests/create');
const getProject = require('../controllers/projectRequirements/get');
const { getServiceRequests } = require('../controllers/serviceRequests/get');
const {
    updateServiceRequest,
} = require('../controllers/serviceRequests/accept');
const { client } = require('../cache');
const { Cloudinary } = require('../cloudinary');
const {
    mongoIdParam,
    siteValidators,
    projectRequirementValidators,
    getSitesQueryValidator,
    createServiceRequestValidators,
    patchServiceRequestValidators,
} = require('../utils/validators');

const upload = multer({ storage: Cloudinary.getStorage() });

const doAuthenticate = createAuthMiddleware.NewAuthenticateMiddleware(client);

// add site router
router.post(
    '/sites',
    siteValidators,
    handleValidationErrors,
    doAuthenticate,
    upload.fields([
        { name: 'encumbranceCertificate', maxCount: 1 },
        { name: 'propertyTaxReceipt', maxCount: 1 },
        { name: 'siteImages', maxCount: 10 },
    ]),
    catchAsync(siteController.addSite)
);

// get site by id
router.get(
    '/sites/:id',
    mongoIdParam('id'),
    handleValidationErrors,
    doAuthenticate,
    catchAsync(siteController.getSiteById)
);

// get sites
router.get(
    '/sites',
    getSitesQueryValidator,
    handleValidationErrors,
    doAuthenticate,
    catchAsync(siteController.getSites)
);

// get sites by user
router.get(
    '/user/sites',
    doAuthenticate,
    catchAsync(siteController.getSitesByUser)
);

// update site router
router.patch(
    '/sites/:id',
    mongoIdParam('id'),
    siteValidators,
    handleValidationErrors,
    doAuthenticate,
    upload.fields([
        { name: 'encumbranceCertificate', maxCount: 1 },
        { name: 'propertyTaxReceipt', maxCount: 1 },
        { name: 'siteImages', maxCount: 10 },
    ]),
    catchAsync(updateSite.updateSite)
);

// delete siteImage by id router
router.delete(
    '/sites/:siteId/assets/:assetId',
    mongoIdParam('siteId'),
    mongoIdParam('assetId'),
    handleValidationErrors,
    doAuthenticate,
    catchAsync(deleteSite.deleteAsset)
);

router.post(
    '/projects',
    doAuthenticate,
    projectRequirementValidators,
    catchAsync(projectRequirement.submitProjectWithRequirement)
);

router.patch(
    '/projects/:projectId',
    mongoIdParam('projectId'),
    doAuthenticate,
    handleValidationErrors,
    projectRequirementValidators,
    catchAsync(updateProject)
);

router.get(
    '/projects/:projectId',
    mongoIdParam('projectId'),
    doAuthenticate,
    handleValidationErrors,
    catchAsync(getProject.getProjectById)
);

router.get('/projects', doAuthenticate, catchAsync(getProject.getProjects));

router.post(
    '/projects/:projectId/service-requests',
    mongoIdParam('projectId'),
    createServiceRequestValidators,
    doAuthenticate,
    handleValidationErrors,
    catchAsync(createServiceRequest)
);

router.get('/service-requests', doAuthenticate, catchAsync(getServiceRequests));

router.patch(
    '/service-request/:projectID',
    mongoIdParam('projectID'),
    patchServiceRequestValidators,
    doAuthenticate,
    handleValidationErrors,
    catchAsync(updateServiceRequest)
);

module.exports = router;
